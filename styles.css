:root {
    --valorant-red: #ff4655;
    --valorant-blue: #0f1923;
    --valorant-dark: #111;
    --valorant-light: #ece8e1;
    --valorant-accent: #00ffb3;
    --valorant-gradient: linear-gradient(135deg, #ff4655 0%, #ff4655 50%, #ff7b86 100%);
    font-size: clamp(11px, 1.5vw, 15px); /* Fluid root font size */
  }

  body {
    background: var(--valorant-blue);
    color: var(--valorant-light);
    min-height: 100vh;
    touch-action: none;
    -ms-touch-action: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    cursor: default;
    margin: 0;
    padding: 0;
    overflow: hidden;
    font-size: 0.95rem; /* Use rem for scaling */
    font-family: 'Montserrat', sans-serif;
  }

  /* Orientation message styling */
  .orientation-message {
    display: none;
    position: fixed;
    inset: 0;
    width: 100vw;
    height: 100vh;
    background: #16213e;
    z-index: 2147483647;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    pointer-events: all;
  }

  .game-content {
    display: block;
  }

  @media (orientation: portrait) {
    .orientation-message {
      display: flex;
    }
    .game-content {
      display: none;
    }
  }

  .game-wrapper {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .player-area {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    padding: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .player-area h2 {
    font-size: 1.1rem;
    margin-bottom: 8px;
  }

  .character-selection {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3px;
    margin-top: 6px;
    flex-grow: 1;
  }

  .character {
    background: rgba(255, 255, 255, 0.2);
    padding: 3px;
    border-radius: 3px;
    cursor: pointer;
    transition: transform 0.3s, background 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85em;
    min-height: 36px;
  }

  .character:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 10px var(--valorant-accent);
  }

  .character.locked {
    filter: grayscale(100%);
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;
  }

  .character.locked::after {
    content: "🔒";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2em;
    color: var(--valorant-red);
  }

  .battlefield-container {
    height: 60vh;
    padding: 2px;
  }

  .battlefield {
    background: #1f2731;
    border: 2px solid var(--valorant-red);
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(255, 70, 85, 0.3);
    height: 100%;
    width: 100%;
    max-height: 380px;
  }

  /* Home Page Styling */
  .home-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100%;
    background: var(--valorant-blue);
    position: relative;
    overflow: hidden;
  }

  .home-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.15;
    z-index: 0;
  }

  .home-content {
    position: relative;
    z-index: 1;
    text-align: center;
    width: 100%;
    max-width: 1200px;
    padding: 20px;
  }

  .game-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 4px;
    margin-bottom: 30px;
    color: var(--valorant-light);
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.7);
  }

  .game-subtitle {
    font-size: clamp(0.9rem, 2vw, 1.1rem);
    margin-bottom: 50px;
    color: var(--valorant-light);
    opacity: 0.8;
  }

  .game-modes {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
  }

  .mode-card {
    background: linear-gradient(135deg, rgba(15, 25, 35, 0.9) 0%, rgba(255, 70, 85, 0.1) 100%);
    border: 1px solid rgba(255, 70, 85, 0.3);
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.2);
    border: 2px solid var(--valorant-red);
    border-radius: 3px;
    padding: 10px;
    width: 180px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 0.95rem;
  }

  .mode-card::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--valorant-gradient);
  }

  .mode-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(255, 70, 85, 0.3);
  }

  .mode-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--valorant-light);
  }

  .mode-card p {
    font-size: 0.9rem;
    color: var(--valorant-light);
    opacity: 0.7;
    margin-bottom: 20px;
  }

  .mode-card .btn {
    background: var(--valorant-red);
    color: var(--valorant-light);
    border: none;
    padding: 8px 20px;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
  }

  .mode-card .btn:hover {
    background: #ff2a3b;
    transform: scale(1.05);
  }

  .mode-card .btn:disabled {
    background: #555;
    cursor: not-allowed;
    opacity: 0.7;
  }

  .user-stats {
    background: linear-gradient(135deg, rgba(15, 25, 35, 0.9) 0%, rgba(255, 70, 85, 0.1) 100%);
    border: 1px solid rgba(255, 70, 85, 0.3);
    border-radius: 5px;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.2);
    flex-wrap: wrap;
    justify-content: center;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--valorant-accent);
  }

  .stat-label {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--valorant-light);
    opacity: 0.7;
  }

  .shop-button, .settings-button, .upgrade-button, .items-button, .equip-button, .logs-button {
    background: var(--valorant-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 3px 10px rgba(255, 70, 85, 0.3);
  }

  .shop-button:hover, .settings-button:hover, .upgrade-button:hover, .items-button:hover, .equip-button:hover, .logs-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.4);
  }

  .unit {
    position: absolute;
    width: 35px;
    height: 35px;
    border-radius: 35%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s ease;
    border: 2px solid #fff;
    box-shadow: 0 0 5px rgba(0,0,0,0.5);
  }

  /* Boss unit styling */
  .unit.boss {
    width: 42px;
    height: 42px;
    font-size: 22px;
    border: 3px solid var(--valorant-red);
    box-shadow: 0 0 15px var(--valorant-red);
    z-index: 10;
    animation: boss-pulse 2s infinite alternate;
  }

  @keyframes boss-pulse {
    0% {
      box-shadow: 0 0 10px var(--valorant-red);
    }
    100% {
      box-shadow: 0 0 20px var(--valorant-red), 0 0 30px rgba(255, 70, 85, 0.5);
    }
  }

  .unit.player1 { background: #4CAF50; }
  .unit.player2 { background: #f44336; }
  .unit.ghost-form { opacity: 1; }

  .health-bar {
    position: absolute;
    bottom: -7px;
    left: 0;
    width: 100%;
    height: 5px;
    background: #333;
    border-radius: 2px;
    overflow: hidden;
  }

  .health-bar-fill {
    height: 100%;
    background: #4CAF50;
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  .health-bar-bleeding {
    background: linear-gradient(90deg, #ff0000 0%, #ff4444 50%, #ff0000 100%);
    animation: bleeding-pulse 1s infinite;
  }

  @keyframes bleeding-pulse {
    0% {
      box-shadow: 0 0 10px red;
      opacity: 0.7;
    }
    50% {
      box-shadow: 0 0 20px red, 0 0 30px darkred;
      opacity: 1;
    }
    100% {
      box-shadow: 0 0 10px red;
      opacity: 0.7;
    }
  }

  .damage-text {
    position: absolute;
    font-weight: bold;
    animation: float 1s forwards;
    pointer-events: none;
    text-shadow: 0 0 5px rgba(0,0,0,0.8);
  }

  .damage-text.normal { color: #ff5555; }
  .damage-text.crit {
    color: #ff0000;
    font-size: 1.5em;
    text-shadow: 0 0 8px #ff0000;
  }
  .damage-text.headshot {
    color: #ff0000;
    font-size: 1.7em;
    text-shadow: 0 0 10px #ff0000;
  }
  .damage-text.miss {
    color: #888;
    font-style: italic;
  }
  .damage-text.slow {
    color: #00ffff;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 0 5px #00ffff;
  }
  .damage-text.freeze {
    color: #87ceeb;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 0 0 10px #00ffff;
    animation: freezeText 0.5s ease-out;
  }
  .damage-text.heal {
    color: #00ff00;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0,255,0,0.5);
  }
  .damage-text.fire {
    color: #ff6600;
    font-weight: bold;
    text-shadow: 0 0 8px #ff6600;
  }

  .fire-aura-effect {
    position: absolute;
    pointer-events: none;
    z-index: 5;
  }

  .fire-effect-indicator {
    color: #ff6600;
    font-size: 12px;
    margin-top: 2px;
    font-weight: bold;
  }

  .fire-effect-info {
    color: #ff6600;
    font-size: 12px;
    margin-top: 2px;
    font-weight: bold;
  }

  @keyframes fireAuraPulse {
    0% {
      transform: scale(0.5);
      opacity: 0;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 0.6;
    }
  }

  @keyframes fireAuraGlow {
    0% {
      transform: scale(0.85);
      opacity: 0.25;
    }
    100% {
      transform: scale(1.1);
      opacity: 0.5;
    }
  }

  .revived { animation: reviveGlow 1s; }
  .countered { animation: counterEffect 0.5s; }

  @keyframes reviveGlow {
    0% { filter: brightness(1.5) drop-shadow(0 0 10px #00ff00); transform: scale(1.2); }
    100% { filter: brightness(1) drop-shadow(0 0 0 transparent); transform: scale(1); }
  }

  @keyframes counterEffect {
    0% { filter: brightness(1); box-shadow: 0 0 0 transparent; }
    50% { filter: brightness(1.5); box-shadow: 0 0 20px #ff9900; }
    100% { filter: brightness(1); box-shadow: 0 0 0 transparent; }
  }

  @keyframes float {
    0% { transform: translateY(0) scale(1); opacity: 1; }
    50% { transform: translateY(-15px) scale(1.3); opacity: 0.7; }
    100% { transform: translateY(-30px) scale(1); opacity: 0; }
  }

  .projectile {
    position: absolute;
    pointer-events: none;
    transition: transform 0.4s ease-out, opacity 0.4s ease-out;
    z-index: 10;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.9);
  }

  .projectile.arrow { font-size: 24px; color: #fff; }
  .projectile.magic { font-size: 24px; color: #ff69b4; filter: drop-shadow(0 0 5px #ff69b4); }
  .projectile.arcane-orb {
    font-size: 20px;
    color: #ff69b4;
    filter: drop-shadow(0 0 8px #ff69b4);
    animation: orbPulse 1s infinite alternate;
  }
  .projectile.slash { font-size: 24px; color: #ffd700; filter: drop-shadow(0 0 5px #ffd700); }
  .projectile.vampire {
    font-size: 24px;
    color: #ff0000;
    filter: drop-shadow(0 0 8px #ff0000);
    text-shadow: 0 0 10px red;
    animation: vampireGlow 0.5s infinite alternate;
  }

  @keyframes vampireGlow {
    from {
      text-shadow: 0 0 5px red;
    }
    to {
      text-shadow: 0 0 15px red, 0 0 20px darkred;
    }
  }

  @keyframes orbPulse {
    from {
      filter: drop-shadow(0 0 8px #ff69b4);
      transform: scale(1);
    }
    to {
      filter: drop-shadow(0 0 15px #ff69b4) drop-shadow(0 0 25px #8a2be2);
      transform: scale(1.1);
    }
  }

  @keyframes iceGlow {
    from {
      filter: drop-shadow(0 0 8px #00ffff);
      text-shadow: 0 0 5px #00ffff;
    }
    to {
      filter: drop-shadow(0 0 15px #00ffff) drop-shadow(0 0 25px #87ceeb);
      text-shadow: 0 0 15px #00ffff, 0 0 20px #87ceeb;
    }
  }

  @keyframes freezeText {
    0% {
      transform: scale(1);
      text-shadow: 0 0 10px #00ffff;
    }
    50% {
      transform: scale(1.3);
      text-shadow: 0 0 20px #00ffff, 0 0 30px #87ceeb;
    }
    100% {
      transform: scale(1);
      text-shadow: 0 0 10px #00ffff;
    }
  }
  .projectile.gun { font-size: 24px; color: #fff; filter: drop-shadow(0 0 5px #fff); }
  .projectile.ice {
    font-size: 20px;
    color: #00ffff;
    filter: drop-shadow(0 0 8px #00ffff);
    animation: iceGlow 0.8s infinite alternate;
  }
  .projectile.meteor { font-size: 40px; color: orange; opacity: 0.95; filter: drop-shadow(0 0 10px orange); }

  @keyframes explosion {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
  }

  .explosion-effect {
    position: absolute;
    width: 150px;
    height: 120px;
    border-radius: 35%;
    background: radial-gradient(circle, rgba(255,200,0,1) 0%, rgba(255,0,0,0.7) 50%, transparent 70%);
    animation: explosion 0.7s ease-out;
    pointer-events: none;
    z-index: 20;
  }

  .drain-effect {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 35%;
    background: radial-gradient(circle, rgba(255,0,0,0.7) 0%, rgba(255,0,0,0.3) 50%, transparent 70%);
    pointer-events: none;
    z-index: 20;
    box-shadow: 0 0 15px red;
    animation: pulse-drain 0.5s infinite alternate;
  }

  @keyframes pulse-drain {
    from {
      box-shadow: 0 0 10px red;
      opacity: 0.7;
    }
    to {
      box-shadow: 0 0 20px red, 0 0 30px darkred;
      opacity: 1;
    }
  }

  .control-area {
    text-align: center;
  }

  .btn {
    padding: 7px 0;
    font-size: 0.95rem;
    margin: 0 5px;
    background: var(--valorant-red);
    color: var(--valorant-light);
    border: none;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    min-height: 36px;
    min-width: 36px;
  }

  .btn:hover {
    background: #ff2a3b;
    transform: scale(1.05);
  }

  .status {
    font-size: 1rem;
    margin-top: 10px;
    color: var(--valorant-light);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  #fullscreenButton {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(15, 25, 35, 0.8);
    color: var(--valorant-light);
    border: 1px solid var(--valorant-red);
    border-radius: 1px;
    padding: 5px 10px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    z-index: 100;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  #fullscreenButton svg { margin-right: 5px; }
  #fullscreenButton:hover {
    background-color: var(--valorant-red);
  }

  /* Shop Styling */
  .shop-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 25, 35, 0.95);
    z-index: 1000;
    overflow-y: auto;
  }

  .upgrade-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 25, 35, 0.95);
    z-index: 1000;
    overflow-y: auto;
  }

  .items-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 25, 35, 0.95);
    z-index: 1000;
    overflow-y: auto;
  }

  .shop-content {
    max-width: 1000px;
    margin: 50px auto;
    padding: 20px;
  }

  .upgrade-content {
    max-width: 1000px;
    margin: 50px auto;
    padding: 20px;
  }

  .items-content {
    max-width: 1000px;
    margin: 50px auto;
    padding: 20px;
  }

  .shop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--valorant-red);
    background: rgba(255, 70, 85, 0.1);
  }

  .upgrade-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--valorant-red);
    background: rgba(255, 70, 85, 0.1);
  }

  .items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--valorant-red);
    background: rgba(255, 70, 85, 0.1);
  }

  .shop-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--valorant-light);
    text-transform: uppercase;
    letter-spacing: 2px;
  }

  .upgrade-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--valorant-light);
    text-transform: uppercase;
    letter-spacing: 2px;
  }

  .items-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--valorant-light);
    text-transform: uppercase;
    letter-spacing: 2px;
  }

  .shop-close, .upgrade-close, .items-close {
    background: var(--valorant-red);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 3px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  .shop-close:hover, .upgrade-close:hover, .items-close:hover {
    background: #ff7b86;
    transform: scale(1.1);
  }

  .shop-balance, .items-balance {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
    color: var(--valorant-accent);
  }

  .shop-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: clamp(10px, 2vw, 30px);
    padding: 20px;
  }

  .upgrade-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: clamp(10px, 2vw, 30px);
    padding: 20px;
  }

  .items-section {
    padding: 20px;
  }

  .items-content-area {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: clamp(10px, 2vw, 30px);
  }

  .shop-item {
    background: rgba(31, 39, 49, 0.8);
    border: 1px solid var(--valorant-red);
    border-radius: 3px;
    padding: 3px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative; /* For proper button positioning */
  }

  .item-element {
    background: rgba(31, 39, 49, 0.8);
    border: 1px solid var(--valorant-red);
    border-radius: 3px;
    padding: 3px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
  }

  .upgrade-item {
    background: rgba(31, 39, 49, 0.8);
    border: 1px solid var(--valorant-red);
    border-radius: 3px;
    padding: 3px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
  }

  .shop-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
  }

  .item-element:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
  }

  .upgrade-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
  }

  .shop-item-icon {
    font-size: 1em;
    margin-bottom: 10px;
  }

  .item-icon {
    font-size: 1em;
    margin-bottom: 10px;
  }

  .upgrade-item-icon {
    font-size: 1em;
    margin-bottom: 10px;
  }

  .shop-item-name {
    font-size: 0.9em;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--valorant-light);
  }

  .item-name {
    font-size: 0.9em;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--valorant-light);
  }

  .shop-item-description {
    font-size: 0.75em;
    color: var(--valorant-accent);
    margin-bottom: 8px;
    line-height: 1.3;
    padding: 0 5px;
    text-align: center;
  }

  .item-description {
    font-size: 0.75em;
    color: var(--valorant-accent);
    margin-bottom: 8px;
    line-height: 1.3;
    padding: 0 5px;
    text-align: center;
  }

  .upgrade-item-name {
    font-size: 0.9em;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--valorant-light);
  }

  .upgrade-item-level {
    font-size: 0.9rem;
    color: var(--valorant-accent);
    margin-bottom: 10px;
    font-weight: 600;
  }

  .upgrade-item-level.max-level {
    color: #ffd700;
    font-weight: 700;
  }

  .upgrade-item-stats {
    margin-bottom: 10px;
    text-align: left;
  }

  .upgrade-stat {
    font-size: 0.8rem;
    color: var(--valorant-light);
    margin-bottom: 2px;
    padding: 2px 0;
  }

  .shop-item-price {
    font-size: 0.9em;
    color: var(--valorant-accent);
    margin-bottom: 15px;
  }

  .item-price {
    font-size: 0.9em;
    color: var(--valorant-accent);
    margin-bottom: 8px;
  }

  .item-quantity {
    font-size: 0.8em;
    color: var(--valorant-light);
    margin-bottom: 15px;
  }

  .item-price {
    font-size: 0.9em;
    color: var(--valorant-accent);
    margin-bottom: 8px;
  }

  .item-quantity {
    font-size: 0.8em;
    color: var(--valorant-light);
    margin-bottom: 15px;
  }

  .upgrade-item-price {
    font-size: 0.9em;
    color: var(--valorant-accent);
    margin-bottom: 15px;
  }

  .upgrade-item-price.max-level {
    color: #ffd700;
    font-weight: 700;
  }

  .shop-item-button {
    background: var(--valorant-red);
    color: var(--valorant-light);
    border: none;
    padding: 7px 0;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    width: 100%;
    min-height: 36px;
    min-width: 36px;
  }

  .shop-item-button:hover {
    background: #ff2a3b;
  }

  .shop-item-button.can-afford {
    background: #4CAF50;
    animation: pulse-button 1.5s infinite;
  }

  .shop-item-button.can-afford:hover {
    background: #3d8b40;
  }

  @keyframes pulse-button {
    0% {
      box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
  }

  .shop-item-button:disabled {
    background: #555;
    cursor: not-allowed;
    animation: none;
  }

  .item-purchase-button {
    background: var(--valorant-red);
    color: var(--valorant-light);
    border: none;
    padding: 7px 0;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    width: 100%;
    min-height: 36px;
    min-width: 36px;
  }

  .item-purchase-button:hover {
    background: #ff2a3b;
  }

  .item-purchase-button.can-afford {
    background: #4CAF50;
    animation: pulse-button 1.5s infinite;
  }

  .item-purchase-button.can-afford:hover {
    background: #3d8b40;
  }

  .item-purchase-button:disabled {
    background: #555;
    cursor: not-allowed;
    animation: none;
  }

  /* Shop Tabs */
  .shop-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--valorant-red);
  }

  .shop-tab {
    background: transparent;
    color: var(--valorant-light);
    border: none;
    padding: 10px 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
  }

  .shop-tab.active {
    color: var(--valorant-red);
  }

  .shop-tab.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--valorant-red);
  }

  .shop-tab:hover:not(.active) {
    color: var(--valorant-accent);
  }

  .shop-tab-content {
    display: none;
  }

  .shop-tab-content.active {
    display: block;
  }

  /* Custom Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(15, 25, 35, 0.5);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--valorant-red);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #ff2a3b;
  }



  /* Story Mode Styling */
  .story-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--valorant-blue);
    z-index: 1000;
    overflow-y: auto;
  }

  .story-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    border-bottom: 2px solid var(--valorant-red);
  }

  .story-title {
    font-size: 1.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--valorant-light);
  }

  .story-back {
    background: transparent;
    color: var(--valorant-light);
    border: 1px solid var(--valorant-light);
    padding: 5px 15px;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
  }

  .story-back:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--valorant-accent);
    color: var(--valorant-accent);
  }

  .stages-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .stage-item {
    background: rgba(31, 39, 49, 0.8);
    border: 1px solid var(--valorant-red);
    border-radius: 3px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
  }

  .stage-item.boss {
    background: rgba(255, 70, 85, 0.2);
    border: 2px solid var(--valorant-red);
  }

  .stage-item.locked {
    filter: grayscale(100%);
    opacity: 0.5;
    cursor: not-allowed;
  }

  .stage-item.locked::after {
    content: "🔒";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5em;
    color: var(--valorant-red);
  }

  .stage-item.completed::before {
    content: "✓";
    position: absolute;
    top: 5px;
    right: 5px;
    color: var(--valorant-accent);
    font-size: 1.2em;
  }

  .stage-item:not(.locked):hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
  }

  .stage-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--valorant-light);
  }

  .stage-label {
    font-size: 0.8rem;
    color: var(--valorant-light);
    opacity: 0.7;
    text-transform: uppercase;
  }

  /* Settings Modal */
  .settings-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 25, 35, 0.95);
    z-index: 1000;
  }

  .settings-content {
    max-width: 600px;
    margin: 50px auto;
    background: var(--valorant-blue);
    border: 2px solid var(--valorant-red);
    border-radius: 3px;
    padding: 20px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--valorant-red);
    padding-bottom: 10px;
  }

  .settings-title {
    font-size: 1.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--valorant-light);
  }

  .settings-close {
    background: transparent;
    color: var(--valorant-light);
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
  }

  .settings-close:hover {
    color: var(--valorant-red);
  }

  .settings-section {
    margin-bottom: 20px;
  }



  .settings-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--valorant-light);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .settings-button-group {
    display: flex;
    gap: 10px;
    margin-top: 10px;
  }

  .settings-button {
    background: var(--valorant-red);
    color: var(--valorant-light);
    border: none;
    padding: 8px 15px;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
  }

  .settings-button:hover {
    background: #ff2a3b;
  }

  .settings-input {
    width: 100%;
    padding: 8px;
    background: rgba(31, 39, 49, 0.8);
    border: 1px solid var(--valorant-red);
    border-radius: 1px;
    color: var(--valorant-light);
    margin-bottom: 10px;
  }

  .logs-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 25, 35, 0.95);
    z-index: 1000;
  }

  .logs-content {
    max-width: 800px;
    margin: 50px auto;
    background: var(--valorant-blue);
    border: 2px solid var(--valorant-red);
    border-radius: 3px;
    padding: 20px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--valorant-red);
    padding-bottom: 10px;
  }

  .logs-title {
    font-size: 1.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--valorant-light);
  }

  .logs-close {
    background: transparent;
    color: var(--valorant-light);
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
  }

  .logs-close:hover {
    color: var(--valorant-red);
  }

  .logs-section {
    margin-bottom: 20px;
  }





  /* Game Header */
  .game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: rgba(15, 25, 35, 0.8);
    border-bottom: 2px solid var(--valorant-red);
    margin-bottom: 10px;
    position: relative;
    z-index: 10;
  }

  .back-button {
    background: transparent;
    color: var(--valorant-light);
    border: 1px solid var(--valorant-light);
    padding: 5px 15px;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .back-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--valorant-accent);
    color: var(--valorant-accent);
  }

  .game-mode-info {
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--valorant-light);
  }

  .coin-display {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 1rem;
    color: var(--valorant-accent);
    font-weight: 600;
  }

  /* Result Screen */
  .result-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 25, 35, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
    display: none;
  }

  .result-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 4px;
    margin-bottom: 20px;
    color: var(--valorant-light);
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.7);
  }

  .result-subtitle {
    font-size: clamp(0.9rem, 2vw, 1.1rem);
    margin-bottom: 30px;
    color: var(--valorant-light);
  }

  .result-reward {
    font-size: 1.8rem;
    color: var(--valorant-accent);
    margin-bottom: 30px;
  }

  .result-buttons {
    display: flex;
    gap: 20px;
  }

  /* Windows-style Notification Styling */
  .windows-notification {
    position: fixed;
    right: 20px;
    width: 300px;
    background-color: var(--valorant-blue);
    color: var(--valorant-light);
    border-radius: 5px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    overflow: hidden;
    transition: all 0.3s ease-out;
    z-index: 9999;
    font-weight: 500;
    opacity: 0;
    transform: translateX(100%);
    margin-bottom: 5px;
    transition: all 0.3s ease-out, bottom 0.3s ease-out;
    border: 1px solid var(--valorant-red);
    font-size: 0.9rem;
  }

  .windows-notification.show {
    opacity: 1;
    transform: translateX(0);
  }

  .windows-notification.hide {
    opacity: 0;
    transform: translateX(100%);
  }

  .notification-header {
    background-color: rgba(31, 39, 49, 0.8);
    padding: 7px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--valorant-red);
  }

  .notification-title {
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .notification-close {
    cursor: pointer;
    font-size: 1.2rem;
  }

  .notification-body {
    padding: 7px;
    word-wrap: break-word;
  }

  .windows-notification {
    animation: notification-fade-in 0.2s ease-out;
  }

  @keyframes notification-fade-in {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .windows-notification .notification-progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.2);
  }

  .windows-notification .notification-progress {
    height: 3px;
    background-color: var(--valorant-accent);
    transition: width 0.1s linear;
  }

  /* Modal System */
  .game-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .game-modal-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  .game-modal {
    background: var(--valorant-blue);
    border: 2px solid var(--valorant-red);
    border-radius: 3px;
    padding: 7px;
    width: 90%;
    max-width: 400px;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    font-size: 0.95rem;
  }

  .game-modal-overlay.show .game-modal {
    transform: translateY(0);
  }

  .game-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--valorant-red);
    padding-bottom: 10px;
  }

  .game-modal-title {
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--valorant-light);
  }

  .game-modal-close {
    background: transparent;
    color: var(--valorant-light);
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
  }

  .game-modal-body {
    margin-bottom: 20px;
    color: var(--valorant-light);
  }

  .game-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  .game-modal-button {
    background: var(--valorant-red);
    color: var(--valorant-light);
    border: none;
    padding: 7px 0;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    min-height: 36px;
    min-width: 36px;
  }

  .game-modal-button.secondary {
    background: transparent;
    border: 1px solid var(--valorant-light);
  }

  .game-modal-button:hover {
    background: #ff2a3b;
    transform: scale(1.05);
  }

  .game-modal-button.secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--valorant-accent);
    color: var(--valorant-accent);
  }

  @media (max-width: 767.98px) {
    .character { padding: 2px; font-size: 0.6em; }
    .unit { width: 20px; height: 20px; font-size: 10px; }
    h2 { font-size: 0.85rem; margin-bottom: 4px; }
    .battlefield-container { height: 40vh; }
    .battlefield { max-height: 180px; }
    .btn { padding: 3px 8px; font-size: 0.7rem; }
    .game-modes { flex-direction: column; align-items: center; gap: 10px; }
    .mode-card { width: 98%; padding: 8px; }
    .stages-grid { grid-template-columns: repeat(4, 1fr); gap: 2px; }
    .shop-item, .upgrade-item, .item-element { padding: 4px; font-size: 0.7em; }
    .shop-item-icon, .upgrade-item-icon, .item-icon { font-size: 1.1em; }
    .shop-item-name, .upgrade-item-name, .item-name { font-size: 0.8em; }
    .shop-item-description, .item-description { font-size: 0.7em; }
    .shop-item-price, .upgrade-item-price, .item-price, .item-quantity { font-size: 0.7em; }
    .shop-item-button, .upgrade-item-button, .item-purchase-button, .change-path-button { padding: 3px 6px; font-size: 0.7rem; }
    .game-modal { padding: 8px; max-width: 95vw; font-size: 0.9em; }
    /* Equip screen mobile styles */
    .equip-char-block, .equip-item-block { padding: 8px; margin-bottom: 6px; }
    .char-name { font-size: 0.9rem; }
    .char-stats { font-size: 0.7rem; }
    .equipped-items-container { gap: 6px; margin: 8px 0; }
    .equipped-item-slot { width: 40px; height: 40px; }
    .equipped-icon { font-size: 1.4rem; }
    .unequip-slot-button { width: 16px; height: 16px; font-size: 10px; top: -6px; right: -6px; }
    .item-icon { font-size: 1.3rem; }
    .item-name { font-size: 0.9rem; }
    .item-stats { font-size: 0.7rem; }
    .equip-buttons { flex-direction: column; gap: 4px; }
    .equip-button, .unequip-button { padding: 4px 8px; font-size: 0.65rem; }
    .game-modal-title { font-size: 1rem; }
    .game-modal-button { padding: 4px 8px; font-size: 0.8rem; }
    .windows-notification { width: 90vw; min-width: 0; font-size: 0.8em; }
    .notification-header, .notification-body { padding: 6px; }
    .result-title { font-size: 1.1rem; }
    .result-subtitle, .result-reward { font-size: 0.9rem; }
    .difficulty-tabs { gap: 4px; margin: 10px 0 12px 0; }
    .difficulty-tab { padding: 6px 0; font-size: 0.8rem; }
    .logs-content { max-width: 95vw; margin: 20px auto; padding: 15px; }
    .logs-title { font-size: 1.4rem; }
    .log-date { font-size: 0.8rem; }
    .log-changes li { font-size: 0.85rem; }
  }

  @media (max-width: 600px) {
    .difficulty-tabs { flex-direction: column; gap: 4px; margin: 10px 0 12px 0; }
    .difficulty-tab { width: 100%; padding: 6px 0; font-size: 1rem; }
  }

  .upgrade-item-button {
    background: var(--valorant-red);
    color: var(--valorant-light);
    border: none;
    padding: 7px 0;
    border-radius: 1px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    width: 100%;
    min-height: 36px;
    min-width: 36px;
  }

  .upgrade-item-button:hover {
    background: #ff2a3b;
  }

  .upgrade-item-button.can-afford {
    background: #4CAF50;
    animation: pulse-button 1.5s infinite;
  }

  .upgrade-item-button.can-afford:hover {
    background: #3d8b40;
  }

  .upgrade-item-button:disabled {
    background: #555;
    cursor: not-allowed;
    animation: none;
  }

  .upgrade-item-button.max-level {
    background: #ffd700;
    color: #000;
    font-weight: 700;
  }

  .upgrade-item-button.max-level:hover {
    background: #666;
    cursor: not-allowed;
  }

  .change-path-button {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 8px;
    width: 100%;
  }

  .change-path-button:hover {
    background: linear-gradient(135deg, #ff7b45 0%, #ffa33e 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
  }

  .change-path-button.can-afford {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    animation: pulse-button 2s infinite;
  }

  .change-path-button.can-afford:hover {
    background: linear-gradient(135deg, #ff7b45 0%, #ffa33e 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 107, 53, 0.6);
  }

  .change-path-button:disabled {
    background: #666;
    color: #999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    animation: none;
  }

  .blood-spear-projectile {
    position: absolute;
    width: 12px;
    height: 60px;
    background: linear-gradient(180deg, #ff0033 60%, #a00000 100%);
    border-radius: 4px 4px 14px 14px;
    box-shadow: 0 0 16px 4px #ff0033, 0 0 8px #a00000 inset;
    z-index: 30;
    opacity: 0.95;
    transition: transform 0.35s cubic-bezier(.7,0,.3,1), opacity 0.2s;
    pointer-events: none;
  }

  .blood-explosion-effect {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 35%;
    background: radial-gradient(circle, #ff0033 0%, #a00000 60%, transparent 100%);
    box-shadow: 0 0 40px 10px #ff0033, 0 0 30px 10px #a00000 inset;
    opacity: 0.85;
    z-index: 40;
    animation: blood-explosion 0.7s cubic-bezier(.7,0,.3,1);
    pointer-events: none;
  }

  @keyframes blood-explosion {
    0% { transform: scale(0.5); opacity: 0.7; }
    60% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(1.0); opacity: 0; }
  }

  .blood-splatter {
    position: absolute;
    width: 18px;
    height: 18px;
    border-radius: 35%;
    background: radial-gradient(circle, #ff0033 60%, #a00000 100%);
    box-shadow: 0 0 8px 2px #a00000;
    opacity: 0.9;
    z-index: 35;
    pointer-events: none;
    transition: transform 0.5s cubic-bezier(.7,0,.3,1), opacity 0.5s;
  }

  /* --- Difficulty Tabs Styling --- */
  .difficulty-tabs {
    display: flex;
    gap: 5px;
    margin: 8px 0 10px 0;
    justify-content: center;
    align-items: center;
  }

  .difficulty-tab {
    background: #181f26;
    color: var(--valorant-light, #fff);
    border: 2px solid var(--valorant-red, #ff4655);
    border-radius: 4px;
    padding: 6px 0;
    font-size: 0.95rem;
    font-weight: 600;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(.4,0,.2,1);
    outline: none;
    box-shadow: 0 2px 8px rgba(255,70,85,0.08);
    position: relative;
  }

  .difficulty-tab.active,
  .difficulty-tab:focus {
    background: var(--valorant-red, #ff4655);
    color: #fff;
    border-color: var(--valorant-red, #ff4655);
    box-shadow: 0 4px 16px rgba(255,70,85,0.18);
    z-index: 1;
  }

  .difficulty-tab:not(.active):hover:not(:disabled) {
    background: #232b33;
    color: var(--valorant-red, #ff4655);
    border-color: var(--valorant-red, #ff4655);
  }

  .difficulty-tab:disabled,
  .difficulty-tab.locked {
    background: #232b33;
    color: #888;
    border-color: #444;
    cursor: not-allowed;
    opacity: 0.7;
    box-shadow: none;
  }

  /* Responsive for small screens */
  @media (max-width: 600px) {
    .difficulty-tabs {
      flex-direction: column;
      gap: 8px;
      margin: 18px 0 20px 0;
    }
    .difficulty-tab {
      width: 100%;
      padding: 10px 0;
      font-size: 1rem;
    }
  }

  @media (min-width: 1200px) {
    html {
      font-size: 18px;
    }
    .game-wrapper, .shop-content, .upgrade-content, .items-content, .settings-content, .logs-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 32px;
    }
    .game-title, .result-title { font-size: 4rem; }
    .game-subtitle, .result-subtitle, .result-reward { font-size: 1.5rem; }
  }

  *:focus-visible {
    outline: 2px solid var(--valorant-accent);
    outline-offset: 2px;
  }

  .stat-label, .stage-label, .notification-title, .game-mode-info {
    color: #fff;
    text-shadow: 0 1px 2px #000, 0 0 2px #000;
  }

  /* High-DPI Mobile (420dpi+, width <= 500px) UX improvements */
  @media (max-width: 500px) and (min-resolution: 3dppx), (max-width: 500px) and (-webkit-min-device-pixel-ratio: 3) {
    html, body {
      font-size: 18px !important;
      max-width: 100vw;
      overflow-x: hidden;
    }
    .game-wrapper, .home-container, .battlefield-container, .battlefield {
      max-width: 100vw !important;
      width: 100vw !important;
      min-width: 0 !important;
      box-sizing: border-box;
      padding: 0 !important;
    }
    .game-title, .result-title {
      font-size: 2.2rem !important;
    }
    .game-subtitle, .result-subtitle, .result-reward {
      font-size: 1.2rem !important;
    }
    .btn, .shop-item-button, .upgrade-item-button, .item-purchase-button, .change-path-button, .game-modal-button {
      font-size: 1.1rem !important;
      padding: 12px 0 !important;
      min-height: 48px !important;
      min-width: 44px !important;
    }
    .mode-card {
      width: 99vw !important;
      padding: 10px !important;
      font-size: 1.1rem !important;
    }
    .character, .shop-item, .upgrade-item, .item-element {
      font-size: 1.1em !important;
      min-height: 44px !important;
    }
    .game-modal {
      max-width: 99vw !important;
      font-size: 1.1rem !important;
      padding: 10px !important;
    }
    .windows-notification {
      width: 98vw !important;
      font-size: 1rem !important;
      min-width: 0 !important;
      left: 1vw !important;
      right: 1vw !important;
    }
    .notification-header, .notification-body {
      padding: 10px !important;
    }
    .result-title, .result-subtitle, .result-reward {
      font-size: 1.1rem !important;
    }
    .difficulty-tabs {
      flex-direction: column !important;
      gap: 6px !important;
      margin: 10px 0 12px 0 !important;
    }
    .difficulty-tab {
      width: 100% !important;
      padding: 10px 0 !important;
      font-size: 1.1rem !important;
    }
    .shop-item-icon, .upgrade-item-icon, .item-icon {
      font-size: 1.3em !important;
    }
    .shop-item-name, .upgrade-item-name, .item-name {
      font-size: 1em !important;
    }
    .shop-item-description, .item-description {
      font-size: 0.85em !important;
    }
    .shop-item-price, .upgrade-item-price, .item-price, .item-quantity {
      font-size: 1em !important;
    }
    /* Equip screen mobile styles */
    .equip-char-block, .equip-item-block {
      padding: 10px !important;
      margin-bottom: 8px !important;
    }
    .char-name {
      font-size: 1rem !important;
    }
    .char-stats {
      font-size: 0.8rem !important;
    }
    .equipped-items-container {
      gap: 6px !important;
      margin: 8px 0 !important;
    }
    .equipped-item-slot {
      width: 40px !important;
      height: 40px !important;
    }
    .equipped-icon {
      font-size: 1.4rem !important;
    }
    .unequip-slot-button {
      width: 16px !important;
      height: 16px !important;
      font-size: 10px !important;
      top: -6px !important;
      right: -6px !important;
    }
    .item-icon {
      font-size: 1.5rem !important;
    }
    .item-name {
      font-size: 1rem !important;
    }
    .item-stats {
      font-size: 0.8rem !important;
    }
    .equip-buttons {
      flex-direction: column !important;
      gap: 6px !important;
    }
    .equip-button, .unequip-button {
      padding: 6px 10px !important;
      font-size: 0.75rem !important;
    }
    /* Prevent horizontal scroll */
    * {
      box-sizing: border-box !important;
    }
  }

  /* Mobile fix: prevent .character-selection overflow and make all characters tappable */
  @media (max-width: 767.98px), (max-width: 500px) and (min-resolution: 3dppx), (max-width: 500px) and (-webkit-min-device-pixel-ratio: 3) {
    .character-selection {
      grid-template-columns: repeat(auto-fit, minmax(44px, 1fr)) !important;
      max-width: 100vw !important;
      overflow-x: hidden !important;
      gap: 4px !important;
      padding: 0 2vw !important;
      box-sizing: border-box !important;
    }
  }

  .projectile.cyborgpunch {
    font-size: 32px;
    color: #00eaff;
    filter: drop-shadow(0 0 12px #00eaff) brightness(1.5);
    text-shadow: 0 0 16px #00eaff, 0 0 32px #00eaff;
    animation: cyborgPunchGlow 0.5s infinite alternate;
  }

  @keyframes cyborgPunchGlow {
    from { text-shadow: 0 0 8px #00eaff; }
    to { text-shadow: 0 0 24px #00eaff, 0 0 48px #00eaff; }
  }

  @keyframes cyborgRechargePulse {
    from { 
      opacity: 0.6; 
      transform: translateX(-50%) scale(0.8);
      text-shadow: 0 0 4px #00eaff;
    }
    to { 
      opacity: 1; 
      transform: translateX(-50%) scale(1.2);
      text-shadow: 0 0 12px #00eaff, 0 0 24px #00eaff;
    }
  }

  .cyborg-recharge-effect {
    pointer-events: none;
    z-index: 100;
  }

  .projectile.lightning {
    font-size: 28px;
    color: #ffff00;
    filter: drop-shadow(0 0 12px #ffff00) brightness(1.5);
    text-shadow: 0 0 16px #ffff00, 0 0 32px #ffff00;
    animation: lightningGlow 0.3s infinite alternate;
  }

  @keyframes lightningGlow {
    from { 
      text-shadow: 0 0 8px #ffff00; 
      transform: scale(1);
    }
    to { 
      text-shadow: 0 0 24px #ffff00, 0 0 48px #ffff00; 
      transform: scale(1.2);
    }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
  }

  .logs-fab {
    position: fixed;
    right: 20px;
    top: 20px;
    z-index: 2000;
  }

  .equip-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 25, 35, 0.95);
    z-index: 1000;
    overflow-y: auto;
  }

  .equip-content {
    max-width: 1000px;
    margin: 50px auto;
    padding: 20px;
  }

  .equip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--valorant-red);
    background: rgba(255, 70, 85, 0.1);
  }

  .equip-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--valorant-light);
    text-transform: uppercase;
    letter-spacing: 2px;
  }

  .equip-close {
    background: var(--valorant-red);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 3px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  .equip-close:hover {
    background: #ff7b86;
    transform: scale(1.1);
  }

  .equip-section {
    padding: 20px;
  }

  .equip-content-area {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .equip-section-title {
    margin-bottom: 15px;
  }

  .equip-section-title h3 {
    color: var(--valorant-accent);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .equip-char-block {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 70, 85, 0.3);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
  }

  .equip-char-block:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--valorant-red);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.2);
  }

  .char-info {
    margin-bottom: 10px;
  }

  .char-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--valorant-light);
    margin-bottom: 5px;
  }

  .char-stats {
    font-size: 0.9rem;
    color: rgba(236, 232, 225, 0.8);
  }

  .equipped-items-container {
    display: flex;
    gap: 8px;
    margin: 10px 0;
    flex-wrap: wrap;
  }

  .equipped-item-slot {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: rgba(0, 255, 179, 0.1);
    border: 2px solid rgba(0, 255, 179, 0.3);
    border-radius: 5px;
    transition: all 0.3s ease;
  }

  .equipped-item-slot:hover {
    background: rgba(0, 255, 179, 0.2);
    border-color: var(--valorant-accent);
    transform: scale(1.05);
  }

  .equipped-item-slot.empty {
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    opacity: 0.5;
  }

  .equipped-item-slot.empty:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.4);
    opacity: 0.8;
  }

  .equipped-icon {
    font-size: 1.8rem;
    color: var(--valorant-accent);
  }

  .equipped-item-slot.empty .equipped-icon {
    color: rgba(255, 255, 255, 0.5);
  }

  .unequip-slot-button {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: var(--valorant-red);
    color: white;
    border: none;
    border-radius: 35%;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
  }

  .unequip-slot-button:hover {
    background: #ff7b86;
    transform: scale(1.2);
  }

  .equipped-item-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .equipped-name {
    font-weight: 500;
    color: var(--valorant-accent);
  }

  .unequip-button {
    background: var(--valorant-red);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .unequip-button:hover {
    background: #ff7b86;
    transform: scale(1.05);
  }

  .equip-item-block {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 70, 85, 0.3);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
  }

  .equip-item-block:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--valorant-red);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.2);
  }

  .item-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
  }

  .item-icon {
    font-size: 2rem;
  }

  .item-details {
    flex: 1;
  }

  .item-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--valorant-light);
    margin-bottom: 5px;
  }

  .item-stats {
    font-size: 0.9rem;
    color: rgba(236, 232, 225, 0.8);
    margin-bottom: 5px;
  }

  .item-quantity {
    font-size: 0.8rem;
    color: var(--valorant-accent);
    font-weight: 500;
  }

  .equip-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .equip-button {
    background: var(--valorant-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 3px 10px rgba(255, 70, 85, 0.3);
  }

  .equip-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.4);
  }

  .no-items {
    text-align: center;
    padding: 30px;
    color: rgba(236, 232, 225, 0.6);
    font-style: italic;
  }

  .no-items p {
    margin: 0;
    font-size: 1rem;
  }

  .equip-select {
    background: #232c36;
    color: var(--valorant-light);
    border: 1.5px solid var(--valorant-accent);
    border-radius: 4px;
    font-size: 1rem;
    font-family: 'Montserrat', sans-serif;
    padding: 8px 14px;
    margin-right: 8px;
    min-width: 220px;
    box-shadow: 0 2px 8px rgba(0,255,179,0.08);
    transition: border 0.2s, box-shadow 0.2s;
  }
  .equip-select:focus {
    outline: none;
    border-color: var(--valorant-red);
    box-shadow: 0 0 0 2px var(--valorant-red), 0 2px 8px rgba(0,255,179,0.12);
  }
  .equip-select option {
    background: #1a222b;
    color: var(--valorant-light);
  }

  /* --- Slot Management Container --- */
  .slot-management-container {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin: 10px auto 15px auto;
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: auto;
    max-width: fit-content;
  }

  /* --- Slot Info and Buy Slot Button Styles --- */
  .slot-info {
    background: var(--valorant-accent);
    color: var(--valorant-blue);
    font-weight: bold;
    border-radius: 4px;
    padding: 10px 15px;
    text-align: center;
    font-size: 1.1em;
    letter-spacing: 1px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    border: 2px solid var(--valorant-accent);
    min-width: 120px;
  }

  .buy-slot-button {
    background: var(--valorant-gradient);
    color: #fff;
    font-weight: 700;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 1em;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(255,70,85,0.15);
    transition: background 0.2s, transform 0.2s, box-shadow 0.2s;
    min-width: 150px;
    white-space: nowrap;
  }
  .buy-slot-button:hover:not(:disabled), .buy-slot-button.can-afford:hover {
    background: linear-gradient(90deg, #ff7b86 0%, #ff4655 100%);
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 4px 16px rgba(255,70,85,0.25);
  }
  .buy-slot-button:disabled {
    background: #888;
    color: #eee;
    opacity: 0.7;
    cursor: not-allowed;
    box-shadow: none;
  }

  /* Random Unit Gacha System Styles */
  .random-unit-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid var(--valorant-accent);
    border-radius: 5px;
    padding: 30px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 255, 179, 0.2);
  }

  .random-unit-header h3 {
    color: var(--valorant-accent);
    margin: 0 0 15px 0;
    font-size: 1.5rem;
    text-shadow: 0 0 10px rgba(0, 255, 179, 0.5);
  }

  .random-unit-header p {
    margin: 5px 0;
    color: #ccc;
    font-size: 0.9rem;
  }

  .guarantee-info {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
    text-align: center;
  }

  .guarantee-info p {
    margin: 3px 0;
  }

  .random-unit-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
  }

  /* Random Item Gacha System Styles */
  .random-item-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid var(--valorant-accent);
    border-radius: 5px;
    padding: 30px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 255, 179, 0.2);
  }

  .random-item-header h3 {
    color: var(--valorant-accent);
    margin: 0 0 15px 0;
    font-size: 1.5rem;
    text-shadow: 0 0 10px rgba(0, 255, 179, 0.5);
  }

  .random-item-header p {
    margin: 5px 0;
    color: #ccc;
    font-size: 0.9rem;
  }

  .random-item-header .guarantee-info {
    background: rgba(255, 152, 0, 0.1);
    border: 1px solid rgba(255, 152, 0, 0.3);
  }

  .random-item-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
  }

  .gacha-box {
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, #2a2a3e, #3a3a5e);
    border: 3px solid #555;
    border-radius: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .gacha-box-inner {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .gacha-question {
    font-size: 48px;
    font-weight: bold;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  }

  /* Rarity-based light effects */
  .gacha-box.common {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }

  .gacha-box.uncommon {
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
  }

  .gacha-box.rare {
    box-shadow: 0 0 20px rgba(0, 100, 255, 0.5);
  }

  .gacha-box.epic {
    box-shadow: 0 0 20px rgba(128, 0, 255, 0.5);
  }

  .gacha-box.legendary {
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.7);
    border-color: #ffd700;
  }

  /* Shaking animation */
  .gacha-box.shaking {
    animation: shake 0.1s infinite;
    cursor: pointer;
    transform-origin: center;
  }

  .gacha-box.shaking:hover {
    filter: brightness(1.2);
    box-shadow: 0 0 40px rgba(255, 255, 255, 0.6) !important;
  }

  .gacha-box.shaking:hover::after {
    content: "คลิกเพื่อข้าม";
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px) rotate(-2deg); }
    75% { transform: translateX(5px) rotate(2deg); }
  }

  /* Explosion animation */
  .gacha-box.exploding {
    animation: explode-box 0.5s ease-out;
  }

  @keyframes explode-box {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); opacity: 0.8; }
    100% { transform: scale(1.1); opacity: 1; }
  }

  /* Revealing animation */
  .gacha-box.revealing {
    animation: reveal 1s ease-out;
  }

  @keyframes reveal {
    0% { opacity: 0.3; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
  }

  /* Explosion particles */
  .explosion-particle {
    --random-x: calc((var(--i, 0) * 37px) - 100px);
    --random-y: calc((var(--i, 0) * -23px) - 50px);
  }

  @keyframes explode {
    0% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(0);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.5) translateX(var(--random-x)) translateY(var(--random-y));
    }
  }

  .random-unit-button {
    background: linear-gradient(135deg, var(--valorant-red) 0%, #ff7b86 100%);
    border: none;
    border-radius: 4px;
    padding: 15px 30px;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
  }

  .random-unit-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 70, 85, 0.4);
  }

  .random-unit-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  /* Drop Rates Table Styles */
  .drop-rates-section {
    margin-top: 30px;
    background: rgba(15, 25, 35, 0.8);
    border: 1px solid var(--valorant-accent);
    border-radius: 5px;
    padding: 20px;
  }

  .drop-rates-section h3 {
    color: var(--valorant-accent);
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    text-align: center;
    text-shadow: 0 0 10px rgba(0, 255, 179, 0.5);
  }

  .drop-rates-table {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
  }

  .drop-rate-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(31, 39, 49, 0.6);
    border: 1px solid rgba(255, 70, 85, 0.3);
    border-radius: 3px;
    transition: all 0.3s ease;
  }

  .drop-rate-row:hover {
    background: rgba(31, 39, 49, 0.8);
    border-color: var(--valorant-accent);
    transform: translateX(5px);
  }

  .character-info, .item-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
  }

  .character-icon, .item-icon {
    font-size: 1.2rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .character-name, .item-name {
    font-weight: 600;
    color: var(--valorant-light);
    text-transform: capitalize;
    flex: 1;
  }

  .drop-rate-percentage {
    font-weight: bold;
    color: var(--valorant-red);
    font-size: 1.1rem;
    min-width: 60px;
    text-align: right;
  }

  /* Status-based styling for drop rate rows */
  .drop-rate-row.unlocked {
    background: rgba(76, 175, 80, 0.15);
    border-color: rgba(76, 175, 80, 0.6);
  }

  .drop-rate-row.unlocked:hover {
    background: rgba(76, 175, 80, 0.25);
    border-color: #4CAF50;
    transform: translateX(8px);
  }

  .drop-rate-row.locked {
    background: rgba(31, 39, 49, 0.6);
    border-color: rgba(255, 70, 85, 0.3);
  }

  .drop-rate-row.locked:hover {
    background: rgba(31, 39, 49, 0.8);
    border-color: rgba(255, 70, 85, 0.5);
    transform: translateX(5px);
  }

  /* Responsive adjustments for drop rates */
  @media (max-width: 768px) {
    .drop-rates-section {
      padding: 15px;
    }

    .drop-rate-row {
      padding: 8px 10px;
      flex-direction: column;
      gap: 5px;
      text-align: center;
    }

    .character-info, .item-info {
      justify-content: center;
    }

    .drop-rate-percentage {
      text-align: center;
    }
  }

  .button-text {
    font-size: 1.1rem;
  }

  .button-cost {
    font-size: 0.9rem;
    opacity: 0.9;
  }

  /* Random Item Button Styles */
  .random-item-button {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border: none;
    border-radius: 5px;
    padding: 15px 30px;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    min-width: 150px;
  }

  .random-item-button .button-text {
    font-size: 1.1rem;
  }

  .random-item-button .button-cost {
    font-size: 0.9rem;
    opacity: 0.9;
  }

  .random-item-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #ff8c42 0%, #ffa726 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
  }

  .random-item-button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
  }

  .random-item-button:disabled {
    background: linear-gradient(135deg, #444 0%, #333 100%);
    color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  /* Upgrade item description styles */
  .upgrade-item-description {
    font-size: 0.8rem;
    color: #ccc;
    margin: 5px 0;
    line-height: 1.3;
    font-style: italic;
    padding: 5px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
    border-left: 3px solid var(--valorant-accent);
  }

  /* Tank growth effect styles */
  .tank-growth-effect {
    animation: tankGrowthPulse 2s ease-in-out;
  }

  @keyframes tankGrowthPulse {
    0% {
      box-shadow: 0 0 30px #4CAF50, 0 0 60px #4CAF50;
      border: 3px solid #4CAF50;
      transform: scale(1);
    }
    50% {
      box-shadow: 0 0 50px #4CAF50, 0 0 100px #4CAF50;
      border: 3px solid #66BB6A;
      transform: scale(1.1);
    }
    100% {
      box-shadow: 0 0 30px #4CAF50, 0 0 60px #4CAF50;
      border: 3px solid #4CAF50;
      transform: scale(1);
    }
  }

  /* Tank revival effect styles */
  .tank-revival-effect {
    animation: tankRevivalGlow 2s ease-in-out;
  }

  @keyframes tankRevivalGlow {
    0% {
      filter: brightness(1.5) drop-shadow(0 0 20px #FFD700);
      border: 3px solid #FFD700;
      transform: scale(1);
    }
    50% {
      filter: brightness(2) drop-shadow(0 0 40px #FFD700);
      border: 3px solid #FFC107;
      transform: scale(1.05);
    }
    100% {
      filter: brightness(1.5) drop-shadow(0 0 20px #FFD700);
      border: 3px solid #FFD700;
      transform: scale(1);
    }
  }

  /* Mobile responsive styles for slot management */
  @media (max-width: 767.98px) {
    .slot-management-container {
      flex-direction: column;
      gap: 10px;
      padding: 8px;
    }

    .slot-info {
      font-size: 1rem;
      padding: 8px 12px;
      min-width: auto;
    }

    .buy-slot-button {
      font-size: 0.9rem;
      padding: 8px 16px;
      min-width: auto;
    }
  }

  /* Daily Event System */
  .event-status {
    margin: 15px 0;
    padding: 0;
  }

  .event-banner {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 12px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    animation: eventGlow 2s ease-in-out infinite alternate;
  }

  .event-icon {
    font-size: 24px;
    margin-right: 12px;
  }

  .event-text {
    color: white;
    font-weight: 600;
    font-size: 14px;
    flex-grow: 1;
  }

  .event-claim-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid white;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .event-claim-btn:hover {
    background: white;
    color: #333;
    transform: scale(1.05);
  }

  @keyframes eventGlow {
    0% { box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); }
    100% { box-shadow: 0 6px 25px rgba(255, 107, 107, 0.4); }
  }

  /* Event Countdown Timer */
  .event-countdown {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9));
    border-radius: 12px;
    padding: 12px 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    min-width: 180px;
  }

  .countdown-content {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
  }

  .countdown-icon {
    font-size: 24px;
    animation: pulse 2s ease-in-out infinite;
  }

  .countdown-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .countdown-event {
    color: white;
    font-size: 12px;
    font-weight: 600;
    opacity: 0.9;
  }

  .countdown-time {
    color: #00ffb3;
    font-size: 14px;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 10px rgba(0, 255, 179, 0.3);
    transition: all 0.3s ease;
  }

  .countdown-minimize {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: white;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 8px;
    font-size: 12px;
  }

  .countdown-minimize:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
  }

  .countdown-time.urgent-time {
    color: #ff4655;
    text-shadow: 0 0 15px rgba(255, 70, 85, 0.5);
    animation: urgentPulse 1s ease-in-out infinite;
  }

  .event-countdown.urgent {
    border-color: rgba(255, 70, 85, 0.3);
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1), rgba(0, 0, 0, 0.9));
    animation: urgentGlow 2s ease-in-out infinite alternate;
  }

  /* Hide/Show animations */
  .event-countdown {
    transform: translateX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .event-countdown.hidden {
    transform: translateX(-100%);
  }

  /* Arrow button to show countdown when hidden */
  .countdown-arrow {
    position: fixed;
    bottom: 20px;
    left: 0px;
    z-index: 1001;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9));
    border-radius: 0 8px 8px 0;
    padding: 12px 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-left: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    color: #00ffb3;
    font-size: 16px;
    transition: all 0.3s ease;
    transform: translateX(0);
  }

  .countdown-arrow.hidden {
    transform: translateX(-100%);
  }

  .countdown-arrow:hover {
    background: linear-gradient(135deg, rgba(0, 255, 179, 0.1), rgba(0, 0, 0, 0.9));
    color: white;
    padding-right: 12px;
    box-shadow: 0 6px 25px rgba(0, 255, 179, 0.2);
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes urgentPulse {
    0%, 100% {
      transform: scale(1);
      text-shadow: 0 0 15px rgba(255, 70, 85, 0.5);
    }
    50% {
      transform: scale(1.05);
      text-shadow: 0 0 20px rgba(255, 70, 85, 0.8);
    }
  }

  @keyframes urgentGlow {
    0% {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 70, 85, 0.3);
    }
    100% {
      box-shadow: 0 6px 30px rgba(255, 70, 85, 0.4);
      border-color: rgba(255, 70, 85, 0.6);
    }
  }

  /* Mobile responsive for countdown */
  @media (max-width: 768px) {
    .event-countdown {
      bottom: 15px;
      left: 15px;
      padding: 10px 12px;
      min-width: 160px;
    }

    .countdown-icon {
      font-size: 20px;
    }

    .countdown-event {
      font-size: 11px;
    }

    .countdown-time {
      font-size: 13px;
    }

    .countdown-minimize {
      width: 20px;
      height: 20px;
      font-size: 10px;
      margin-left: 6px;
    }

    .countdown-arrow {
      bottom: 15px;
      padding: 10px 6px;
      font-size: 14px;
    }

    .countdown-arrow:hover {
      padding-right: 10px;
    }
  }